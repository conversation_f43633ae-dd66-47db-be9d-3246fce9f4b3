import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../utils/responsive.dart';

class InsightsScreen extends StatelessWidget {
  const InsightsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plant Health Insights'),
        backgroundColor: AppConstants.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildComingSoonCard(context),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildFeaturesList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonCard(BuildContext context) {
    return Card(
      elevation: AppConstants.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppConstants.primaryGreen.withOpacity(0.1),
              AppConstants.lightGreen.withOpacity(0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.insights,
              size: 80,
              color: AppConstants.primaryGreen,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              'Plant Health Insights',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryGreen,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Coming Soon in Phase 2!',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppConstants.textSecondary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Get AI-powered insights about your plants\' health, care recommendations, and growth tracking.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList(BuildContext context) {
    final features = [
      {
        'icon': Icons.health_and_safety,
        'title': 'Health Analysis',
        'description': 'AI-powered analysis of plant health from photos',
      },
      {
        'icon': Icons.water_drop,
        'title': 'Watering Schedule',
        'description': 'Personalized watering recommendations',
      },
      {
        'icon': Icons.wb_sunny,
        'title': 'Light Requirements',
        'description': 'Optimal lighting conditions for each plant',
      },
      {
        'icon': Icons.thermostat,
        'title': 'Climate Monitoring',
        'description': 'Temperature and humidity recommendations',
      },
      {
        'icon': Icons.bug_report,
        'title': 'Disease Detection',
        'description': 'Early detection of plant diseases and pests',
      },
      {
        'icon': Icons.trending_up,
        'title': 'Growth Tracking',
        'description': 'Monitor and track plant growth over time',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upcoming Features',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        ...features.map((feature) => _buildFeatureCard(context, feature)),
      ],
    );
  }

  Widget _buildFeatureCard(BuildContext context, Map<String, dynamic> feature) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: AppConstants.elevationLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
        leading: Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Icon(
            feature['icon'] as IconData,
            color: AppConstants.primaryGreen,
            size: 24,
          ),
        ),
        title: Text(
          feature['title'] as String,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: AppConstants.paddingSmall),
          child: Text(
            feature['description'] as String,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textSecondary,
            ),
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingSmall,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          ),
          child: Text(
            'Soon',
            style: TextStyle(
              color: Colors.orange.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
