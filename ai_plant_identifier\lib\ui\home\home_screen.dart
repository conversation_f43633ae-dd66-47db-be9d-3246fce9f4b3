import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/plant_model.dart';
import '../../services/local_storage_service.dart';
import '../../services/image_service.dart';
import '../../utils/constants.dart';
import '../../utils/responsive.dart';
import '../shared_widgets/custom_button.dart';
import '../shared_widgets/custom_dropdown.dart';
import '../shared_widgets/image_picker_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  File? _selectedImage;
  String? _selectedPlantType;
  bool _isIdentifying = false;

  final ImageService _imageService = ImageService();
  final LocalStorageService _storageService = LocalStorageService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Plant Identifier'),
        backgroundColor: AppConstants.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: ResponsiveHelper.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(context),
              const SizedBox(height: AppConstants.paddingLarge),
              _buildPlantTypeDropdown(),
              const SizedBox(height: AppConstants.paddingLarge),
              _buildImagePickerSection(),
              const SizedBox(height: AppConstants.paddingLarge),
              _buildIdentifyButton(),
              const SizedBox(height: AppConstants.paddingLarge),
              _buildRecentPlantsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppConstants.primaryGreen.withOpacity(0.1),
            AppConstants.lightGreen.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Plant Identifier!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppConstants.primaryGreen,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Take a photo or select an image from your gallery to identify any plant instantly.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlantTypeDropdown() {
    return CustomDropdown(
      label: 'Plant Type (Optional)',
      hint: 'Select plant type to improve accuracy',
      value: _selectedPlantType,
      items: AppConstants.plantTypes,
      prefixIcon: Icons.local_florist,
      onChanged: (value) {
        setState(() {
          _selectedPlantType = value;
        });
      },
    );
  }

  Widget _buildImagePickerSection() {
    return ImagePickerCard(
      label: 'Plant Image',
      imageFile: _selectedImage,
      onTapGallery: _pickImageFromGallery,
      onTapCamera: _pickImageFromCamera,
      height: ResponsiveHelper.getProportionateScreenHeight(context, 300),
    );
  }

  Widget _buildIdentifyButton() {
    final bool canIdentify = _selectedImage != null;
    
    return CustomButton(
      text: _isIdentifying ? 'Identifying Plant...' : 'Identify Plant',
      onPressed: canIdentify ? _identifyPlant : null,
      enabled: canIdentify && !_isIdentifying,
      isLoading: _isIdentifying,
      icon: Icons.search,
    );
  }

  Widget _buildRecentPlantsSection() {
    final recentPlants = _storageService.getRecentPlants(limit: 3);
    
    if (recentPlants.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Identifications',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recentPlants.length,
            itemBuilder: (context, index) {
              final plant = recentPlants[index];
              return _buildRecentPlantCard(plant);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecentPlantCard(PlantModel plant) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
      child: Card(
        elevation: AppConstants.elevationLow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        child: Column(
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Image.file(
                  File(plant.imagePath),
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              child: Text(
                plant.identifiedName ?? plant.type,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromGallery() async {
    final image = await _imageService.pickImageFromGallery();
    if (image != null) {
      setState(() {
        _selectedImage = image;
      });
    }
  }

  Future<void> _pickImageFromCamera() async {
    final image = await _imageService.pickImageFromCamera();
    if (image != null) {
      setState(() {
        _selectedImage = image;
      });
    }
  }

  Future<void> _identifyPlant() async {
    if (_selectedImage == null) {
      _showErrorSnackBar(AppConstants.noImageSelectedError);
      return;
    }

    setState(() {
      _isIdentifying = true;
    });

    try {
      // Simulate AI processing delay
      await Future.delayed(const Duration(seconds: 2));

      // For now, create a mock identification result
      // In Phase 2, this will be replaced with actual AI/CV logic
      final plant = PlantModel(
        imagePath: _selectedImage!.path,
        type: _selectedPlantType ?? 'Unknown',
        timestamp: DateTime.now(),
        identifiedName: _getMockPlantName(),
        scientificName: _getMockScientificName(),
        description: _getMockDescription(),
        confidence: 0.85 + (DateTime.now().millisecond % 15) / 100, // Mock confidence
      );

      // Save to local storage
      await _storageService.addPlantToHistory(plant);

      // Show success message
      _showSuccessSnackBar('Plant identified successfully!');

      // Reset form
      setState(() {
        _selectedImage = null;
        _selectedPlantType = null;
      });

    } catch (e) {
      _showErrorSnackBar('Failed to identify plant. Please try again.');
    } finally {
      setState(() {
        _isIdentifying = false;
      });
    }
  }

  String _getMockPlantName() {
    final names = [
      'Rose', 'Sunflower', 'Tulip', 'Daisy', 'Lily',
      'Oak Tree', 'Pine Tree', 'Maple Tree', 'Fern', 'Cactus'
    ];
    return names[DateTime.now().millisecond % names.length];
  }

  String _getMockScientificName() {
    final names = [
      'Rosa rubiginosa', 'Helianthus annuus', 'Tulipa gesneriana',
      'Bellis perennis', 'Lilium candidum', 'Quercus robur',
      'Pinus sylvestris', 'Acer platanoides', 'Pteridium aquilinum',
      'Opuntia ficus-indica'
    ];
    return names[DateTime.now().millisecond % names.length];
  }

  String _getMockDescription() {
    return 'This is a beautiful plant species commonly found in gardens and natural habitats. It requires moderate sunlight and regular watering.';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
