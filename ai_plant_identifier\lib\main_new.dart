import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'models/plant_model.dart';
import 'models/history_model.dart';
import 'utils/constants.dart';
import 'utils/theme.dart';
import 'ui/onboarding/onboarding_screen.dart';
import 'ui/shared_widgets/app_navigator.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();
  
  // Register Hive adapters
  Hive.registerAdapter(PlantModelAdapter());
  Hive.registerAdapter(HistoryModelAdapter());
  
  // Open Hive boxes
  await Hive.openBox<PlantModel>(AppConstants.plantsBoxName);
  await Hive.openBox<HistoryModel>(AppConstants.historyBoxName);

  // Check if onboarding has been seen
  final prefs = await SharedPreferences.getInstance();
  final hasSeenOnboarding = prefs.getBool(AppConstants.hasSeenOnboardingKey) ?? true;

  runApp(MyApp(showOnboarding: hasSeenOnboarding));
}

class MyApp extends StatelessWidget {
  final bool showOnboarding;

  const MyApp({
    Key? key,
    required this.showOnboarding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      home: showOnboarding
          ? OnboardingScreen(
              onComplete: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const AppNavigator(),
                  ),
                );
              },
            )
          : const AppNavigator(),
      debugShowCheckedModeBanner: false,
    );
  }
}
