import 'package:flutter/material.dart';
import '../../utils/constants.dart';
import '../../utils/responsive.dart';

class OnboardingPage extends StatelessWidget {
  final String imageAsset;
  final String title;
  final String description;

  const OnboardingPage({
    Key? key,
    required this.imageAsset,
    required this.title,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = ResponsiveHelper.isLandscape(context);
    
    return SafeArea(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: isLandscape 
            ? _buildLandscapeLayout(context, size)
            : _buildPortraitLayout(context, size),
      ),
    );
  }

  Widget _buildPortraitLayout(BuildContext context, Size size) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(flex: 1),
        _buildImageContainer(context, size),
        const SizedBox(height: AppConstants.paddingXLarge),
        _buildTextContent(context),
        const Spacer(flex: 2),
      ],
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, Size size) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: _buildImageContainer(context, size),
        ),
        const SizedBox(width: AppConstants.paddingLarge),
        Expanded(
          flex: 1,
          child: _buildTextContent(context),
        ),
      ],
    );
  }

  Widget _buildImageContainer(BuildContext context, Size size) {
    final imageSize = ResponsiveHelper.isMobile(context) 
        ? size.width * 0.7 
        : ResponsiveHelper.getImageSize(context);
    
    return Container(
      width: imageSize,
      height: imageSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppConstants.primaryGreen.withOpacity(0.1),
            AppConstants.lightGreen.withOpacity(0.1),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryGreen.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipOval(
        child: _buildImage(),
      ),
    );
  }

  Widget _buildImage() {
    // For now, we'll use a placeholder since we don't have actual images
    // In a real app, you would use: Image.asset(imageAsset, fit: BoxFit.cover)
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppConstants.primaryGreen.withOpacity(0.3),
            AppConstants.lightGreen.withOpacity(0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.local_florist,
        size: 120,
        color: AppConstants.primaryGreen,
      ),
    );
  }

  Widget _buildTextContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.displaySmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppConstants.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 24),
          ),
        ),
        const SizedBox(height: AppConstants.paddingLarge),
        Text(
          description,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppConstants.textSecondary,
            height: 1.5,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
          ),
        ),
      ],
    );
  }
}

class OnboardingIndicator extends StatelessWidget {
  final int currentIndex;
  final int totalPages;

  const OnboardingIndicator({
    Key? key,
    required this.currentIndex,
    required this.totalPages,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalPages, (index) {
        return AnimatedContainer(
          duration: AppConstants.animationMedium,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: currentIndex == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: currentIndex == index
                ? AppConstants.primaryGreen
                : AppConstants.primaryGreen.withOpacity(0.3),
          ),
        );
      }),
    );
  }
}

class OnboardingButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isOutlined;

  const OnboardingButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isOutlined = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isOutlined) {
      return OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: AppConstants.primaryGreen,
          side: const BorderSide(color: AppConstants.primaryGreen),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppConstants.primaryGreen,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingLarge,
          vertical: AppConstants.paddingMedium,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        elevation: AppConstants.elevationLow,
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
