import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'AI Plant Identifier';
  static const String appVersion = '1.0.0';
  
  // Colors
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color lightGreen = Color(0xFF8BC34A);
  static const Color darkGreen = Color(0xFF2E7D32);
  static const Color accentGreen = Color(0xFFC8E6C9);
  
  // Secondary Colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Colors.white;
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color successColor = Color(0xFF388E3C);
  
  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border Radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 20.0;
  
  // Elevation
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  
  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  // Plant Types
  static const List<String> plantTypes = [
    'Flower',
    'Tree',
    'Shrub',
    'Succulent',
    'Herb',
    'Vine',
    'Fern',
    'Grass',
    'Moss',
    'Other',
  ];
  
  // Onboarding Data
  static const List<Map<String, String>> onboardingData = [
    {
      'title': "Explore Nature's Diversity",
      'description': "Build your personal plant collection, learn about different species, and deepen your connection with the natural world.",
      'image': 'assets/images/onboarding1.png',
    },
    {
      'title': "Discover Plant Details & Care",
      'description': "Get rich information, including common & scientific names, characteristics, and essential care tips for your identified plants.",
      'image': 'assets/images/onboarding2.png',
    },
    {
      'title': "Snap a Plant, Unveil Its Name",
      'description': "Use your phone's camera to instantly identify flowers, trees, and other plants around you. It's quick and easy!",
      'image': 'assets/images/onboarding3.png',
    },
  ];
  
  // SharedPreferences Keys
  static const String hasSeenOnboardingKey = 'hasSeenOnboarding';
  static const String userPreferencesKey = 'userPreferences';
  static const String appSettingsKey = 'appSettings';
  
  // Hive Box Names
  static const String plantsBoxName = 'plantsBox';
  static const String historyBoxName = 'historyBox';
  static const String settingsBoxName = 'settingsBox';
  
  // Image Constraints
  static const int maxImageWidth = 1024;
  static const int maxImageHeight = 1024;
  static const int imageQuality = 85;
  
  // UI Constraints
  static const double maxCardWidth = 400.0;
  static const double minButtonHeight = 48.0;
  static const double maxButtonHeight = 56.0;
  
  // Error Messages
  static const String noImageSelectedError = 'Please select an image first';
  static const String noPlantTypeSelectedError = 'Please select a plant type';
  static const String imagePickerError = 'Failed to pick image';
  static const String storageError = 'Failed to save data';
  static const String networkError = 'Network connection failed';
}
